"""
MongoDB Pipeline for storing legal documents and avoiding duplicates.

This pipeline connects to MongoDB and stores articles in a structured format
while checking for duplicates based on article_id.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

from pymongo import MongoClient, errors
from pymongo.collection import Collection
from pymongo.database import Database
from itemadapter import ItemAdapter

from .items import ArticleItem


class MongoDBPipeline:
    """Pipeline for storing articles in MongoDB with duplicate checking."""
    
    def __init__(self, mongo_uri: str = "mongodb://localhost:27017/", 
                 mongo_db: str = "legal_documents", 
                 mongo_collection: str = "documents"):
        """
        Initialize MongoDB pipeline.
        
        Args:
            mongo_uri: MongoDB connection URI
            mongo_db: Database name
            mongo_collection: Collection name
        """
        self.mongo_uri = mongo_uri
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        self.client: Optional[MongoClient] = None
        self.db: Optional[Database] = None
        self.collection: Optional[Collection] = None
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            'items_processed': 0,
            'items_stored': 0,
            'items_skipped_duplicate': 0,
            'items_updated': 0,
            'errors': 0
        }

    @classmethod
    def from_crawler(cls, crawler):
        """Create pipeline instance from crawler settings."""
        return cls(
            mongo_uri=crawler.settings.get("MONGO_URI", "mongodb://localhost:27017/"),
            mongo_db=crawler.settings.get("MONGO_DATABASE", "legal_documents"),
            mongo_collection=crawler.settings.get("MONGO_COLLECTION", "documents"),
        )

    def open_spider(self, spider):
        """Initialize MongoDB connection when spider opens."""
        try:
            self.client = MongoClient(self.mongo_uri)
            self.db = self.client[self.mongo_db]
            self.collection = self.db[self.mongo_collection]
            
            # Test connection
            self.client.admin.command('ping')
            
            # Create indexes for better performance
            self.create_indexes()
            
            self.logger.info(f"✅ Connected to MongoDB: {self.mongo_uri}")
            self.logger.info(f"📁 Database: {self.mongo_db}, Collection: {self.mongo_collection}")
            
        except errors.ConnectionFailure as e:
            self.logger.error(f"❌ Failed to connect to MongoDB: {e}")
            raise
        except Exception as e:
            self.logger.error(f"❌ Error initializing MongoDB pipeline: {e}")
            raise

    def close_spider(self, spider):
        """Close MongoDB connection and log statistics when spider closes."""
        if self.client:
            self.client.close()
            self.logger.info("🔌 MongoDB connection closed")
        
        # Log final statistics
        self.logger.info("📊 MongoDB Pipeline Statistics:")
        self.logger.info(f"   Items processed: {self.stats['items_processed']}")
        self.logger.info(f"   Items stored: {self.stats['items_stored']}")
        self.logger.info(f"   Items updated: {self.stats['items_updated']}")
        self.logger.info(f"   Duplicates skipped: {self.stats['items_skipped_duplicate']}")
        self.logger.info(f"   Errors: {self.stats['errors']}")

    def create_indexes(self):
        """Create database indexes for better performance."""
        try:
            # Create unique index on article_id to prevent duplicates
            self.collection.create_index("article_id", unique=True)
            
            # Create indexes for common queries
            self.collection.create_index("year")
            self.collection.create_index("month")
            self.collection.create_index("mudda_type")
            self.collection.create_index("created_at")
            self.collection.create_index([("year", 1), ("month", 1), ("mudda_type", 1)])
            
            self.logger.info("📇 Created MongoDB indexes")
            
        except Exception as e:
            self.logger.warning(f"⚠️ Could not create indexes: {e}")

    def process_item(self, item, spider):
        """Process and store item in MongoDB."""
        if not isinstance(item, ArticleItem):
            return item
            
        adapter = ItemAdapter(item)
        article_id = adapter.get('article_id')
        
        if not article_id:
            self.logger.warning("⚠️ Item missing article_id, skipping")
            self.stats['errors'] += 1
            return item
            
        self.stats['items_processed'] += 1
        
        try:
            # Check if article already exists
            existing_doc = self.collection.find_one({"article_id": article_id})
            
            if existing_doc:
                # Article exists, check if we should update it
                if self.should_update_document(existing_doc, adapter):
                    updated_doc = self.prepare_document(adapter, update=True)
                    self.collection.update_one(
                        {"article_id": article_id},
                        {"$set": updated_doc}
                    )
                    self.stats['items_updated'] += 1
                    self.logger.info(f"🔄 Updated article {article_id} in MongoDB")
                else:
                    self.stats['items_skipped_duplicate'] += 1
                    self.logger.debug(f"⏭️ Skipped duplicate article {article_id}")
            else:
                # New article, insert it
                document = self.prepare_document(adapter)
                self.collection.insert_one(document)
                self.stats['items_stored'] += 1
                self.logger.info(f"💾 Stored new article {article_id} in MongoDB")
                
        except errors.DuplicateKeyError:
            # Handle race condition where document was inserted between check and insert
            self.stats['items_skipped_duplicate'] += 1
            self.logger.debug(f"⏭️ Duplicate key for article {article_id}, skipping")
            
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"❌ Error processing article {article_id}: {e}")
            
        return item

    def prepare_document(self, adapter: ItemAdapter, update: bool = False) -> Dict[str, Any]:
        """
        Prepare document for MongoDB storage.
        
        Args:
            adapter: ItemAdapter containing article data
            update: Whether this is an update operation
            
        Returns:
            Dictionary ready for MongoDB storage
        """
        now = datetime.utcnow()
        
        # Base document structure
        document = {
            "article_id": adapter.get('article_id'),
            "url": adapter.get('url'),
            "title": adapter.get('title', ''),
            
            # Classification
            "year": adapter.get('year'),
            "month": adapter.get('month'),
            "mudda_type": adapter.get('mudda_type'),
            
            # Metadata
            "decision_date": adapter.get('decision_date', ''),
            "views": adapter.get('views', ''),
            "court_type": adapter.get('court_type', ''),
            "parties": adapter.get('parties', {}),
            "summary": adapter.get('summary', ''),
            
            # Content
            "text": adapter.get('text', ''),
            "actual_cleaned_text": adapter.get('actual_cleaned_text', ''),
            "content": adapter.get('content', {}),
            "structured_content": adapter.get('structured_content', {}),
            
            # Extraction metadata
            "content_extracted": adapter.get('content_extracted', False),
            "extraction_info": adapter.get('extraction_info', {}),
            "metadata": adapter.get('metadata', {}),
            
            # File system reference
            "file_path": adapter.get('file_path'),
            
            # Timestamps
            "scraped_at": adapter.get('scraped_at'),
            "updated_at": now.isoformat(),
        }
        
        # Add created_at only for new documents
        if not update:
            document["created_at"] = now.isoformat()
            
        # Remove None values to keep document clean
        document = {k: v for k, v in document.items() if v is not None}
        
        return document

    def should_update_document(self, existing_doc: Dict[str, Any], 
                             adapter: ItemAdapter) -> bool:
        """
        Determine if an existing document should be updated.
        
        Args:
            existing_doc: Existing document from MongoDB
            adapter: New item data
            
        Returns:
            True if document should be updated, False otherwise
        """
        # Update if content was not previously extracted but now is
        if (not existing_doc.get('content_extracted', False) and 
            adapter.get('content_extracted', False)):
            return True
            
        # Update if new content is significantly longer
        existing_text_length = len(existing_doc.get('text', ''))
        new_text_length = len(adapter.get('text', ''))
        
        if new_text_length > existing_text_length * 1.1:  # 10% longer
            return True
            
        # Update if extraction info is newer
        existing_extracted_at = existing_doc.get('extraction_info', {}).get('extracted_at')
        new_extracted_at = adapter.get('extraction_info', {}).get('extracted_at')
        
        if existing_extracted_at and new_extracted_at:
            if new_extracted_at > existing_extracted_at:
                return True
                
        return False

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the MongoDB collection."""
        if not self.collection:
            return {}
            
        try:
            total_docs = self.collection.count_documents({})
            
            # Count by year
            year_pipeline = [
                {"$group": {"_id": "$year", "count": {"$sum": 1}}},
                {"$sort": {"_id": 1}}
            ]
            year_counts = list(self.collection.aggregate(year_pipeline))
            
            # Count by mudda_type
            mudda_pipeline = [
                {"$group": {"_id": "$mudda_type", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}}
            ]
            mudda_counts = list(self.collection.aggregate(mudda_pipeline))
            
            # Count extracted vs not extracted
            extracted_count = self.collection.count_documents({"content_extracted": True})
            
            return {
                "total_documents": total_docs,
                "extracted_content": extracted_count,
                "not_extracted": total_docs - extracted_count,
                "by_year": {item["_id"]: item["count"] for item in year_counts},
                "by_mudda_type": {item["_id"]: item["count"] for item in mudda_counts}
            }
            
        except Exception as e:
            self.logger.error(f"Error getting collection stats: {e}")
            return {}
