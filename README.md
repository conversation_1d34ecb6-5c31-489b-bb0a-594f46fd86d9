# NPK Scraper

A Scrapy-based web scraper for the Nepal Supreme Court (NKP) website to extract legal documents and case information with full content extraction.

## Features

- 🕷️ **Advanced Web Scraping**: Scrapes articles from https://nkp.gov.np/advance_search
- 📄 **Complete Content Extraction**: Extracts full article text, metadata, and structured content using regex
- 🛡️ **Anti-Blocking Measures**: User agent rotation, random delays, and conservative request rates
- 📊 **Structured Data**: Organizes content into metadata, full text, and structured sections (judges, parties, legal acts)
- 🔄 **Resume Capability**: Progress tracking allows resuming interrupted scraping sessions
- 📁 **Hierarchical Output**: Data organized by year → mudda type → month → articles

## Installation

1. Clone the repository
2. Install dependencies using uv:
   ```bash
   uv sync
   ```

## Usage

Run the main spider:
```bash
scrapy crawl nkp_url_collector
```

For testing with limited items:
```bash
scrapy crawl nkp_url_collector -s CLOSESPIDER_ITEMCOUNT=5 -L INFO
```

## Output Structure

```json
{
  "collection_info": {
    "year": "2080",
    "total_articles": 150,
    "scraped_at": "2025-01-29T..."
  },
  "data": {
    "year_2080": {
      "mudda_type_1_दुनियाबादी_देवानी": {
        "month_1_बैशाख": [
          {
            "article_id": "10313",
            "title": "निर्णय नं. ११२१८...",
            "url": "https://nkp.gov.np/full_detail/10313",
            "metadata": {
              "decision_date": "२०७९/०८/१२",
              "views": "787",
              "edition": {...}
            },
            "content": {
              "text": "Complete article text...",
              "paragraphs": [...],
              "paragraph_count": 45
            },
            "structured_content": {
              "judges": [...],
              "case_parties": [...],
              "legal_acts": [...],
              "decision_text": [...]
            }
          }
        ]
      }
    }
  }
}
```

## Configuration

The spider includes built-in anti-blocking measures:
- Random delays (2-8 seconds)
- User agent rotation (6 different agents)
- Conservative concurrency (0.5 requests/domain)
- HTTP caching and retry logic

## Dependencies

- `scrapy>=2.13.3` - Web scraping framework
- `requests>=2.32.4` - HTTP library for testing