# Define here the models for your spider middleware
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/spider-middleware.html

from scrapy import signals
from scrapy.http import HtmlResponse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time
import logging

# useful for handling different item types with a single interface
from itemadapter import ItemAdapter


class SeleniumMiddleware:
    """Selenium middleware for handling JavaScript-rendered content"""

    def __init__(self):
        self.driver = None
        self.logger = logging.getLogger(__name__)

    @classmethod
    def from_crawler(cls, crawler):
        return cls()

    def open_spider(self, spider):
        """Initialize Selenium driver when spider opens - using working config from a.py"""
        from selenium.webdriver.chrome.service import Service
        import os

        chrome_options = Options()
        chrome_options.headless = True
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        # Try different Chrome/Chromium binary locations
        chrome_binaries = [
            "/usr/lib/chromium/chromium",
            "/usr/bin/chromium",
            "/usr/bin/chromium-browser",
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable"
        ]

        chrome_binary = None
        for binary in chrome_binaries:
            if os.path.exists(binary):
                chrome_binary = binary
                break

        if chrome_binary:
            chrome_options.binary_location = chrome_binary
            self.logger.info(f"Using Chrome binary: {chrome_binary}")
        else:
            self.logger.warning("No Chrome binary found, trying default")

        # Try different ChromeDriver locations
        driver_paths = [
            "/usr/local/bin/chromedriver",
            "/usr/bin/chromedriver",
            "chromedriver"  # System PATH
        ]

        driver_path = None
        for path in driver_paths:
            if path == "chromedriver" or os.path.exists(path):
                driver_path = path
                break

        try:
            if driver_path and driver_path != "chromedriver":
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # Try without explicit service path
                self.driver = webdriver.Chrome(options=chrome_options)

            self.logger.info(f"Selenium Chrome driver initialized successfully using {driver_path or 'default'}")
        except Exception as e:
            self.logger.error(f"Failed to initialize Selenium driver: {e}")
            self.logger.error("Make sure ChromeDriver is installed:")
            self.logger.error("  sudo apt-get install chromium-chromedriver")
            self.logger.error("  or download from: https://chromedriver.chromium.org/")
            self.driver = None

    def close_spider(self, spider):
        """Close Selenium driver when spider closes"""
        if self.driver:
            self.driver.quit()
            self.logger.info("Selenium driver closed")

    def process_request(self, request, spider):
        """Process request using Selenium if needed"""
        # Only use Selenium for specific URLs or when explicitly requested
        if (request.meta.get('use_selenium', False) or
            'full_detail' in request.url or
            'advance_search' in request.url):

            if not self.driver:
                self.logger.error("Selenium driver not available")
                return None

            try:
                self.logger.info(f"Processing with Selenium: {request.url}")

                # Load the page
                self.driver.get(request.url)

                # Wait for page to load
                time.sleep(2)

                # Wait for specific elements if it's an article detail page - using working approach from a.py
                if 'full_detail' in request.url:
                    try:
                        # Wait for the article element like in a.py
                        article = WebDriverWait(self.driver, 15).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "article.type-post"))
                        )
                        self.logger.info("Article content loaded successfully")
                    except TimeoutException:
                        self.logger.warning("Timeout waiting for article content")

                # Wait for search form if it's the advance search page
                elif 'advance_search' in request.url:
                    try:
                        # Wait for the form to load
                        WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.NAME, "year"))
                        )
                        self.logger.info("Search form loaded successfully")
                    except TimeoutException:
                        self.logger.warning("Timeout waiting for search form")

                # Additional wait for any remaining JavaScript
                time.sleep(1)

                # Get the page source
                body = self.driver.page_source

                # Create a new response with the rendered content
                return HtmlResponse(
                    url=request.url,
                    body=body,
                    encoding='utf-8',
                    request=request
                )

            except Exception as e:
                self.logger.error(f"Error processing request with Selenium: {e}")
                return None

        # For other requests, let Scrapy handle them normally
        return None


class NpkScraperSpiderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the spider middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_spider_input(self, response, spider):
        # Called for each response that goes through the spider
        # middleware and into the spider.

        # Should return None or raise an exception.
        return None

    def process_spider_output(self, response, result, spider):
        # Called with the results returned from the Spider, after
        # it has processed the response.

        # Must return an iterable of Request, or item objects.
        for i in result:
            yield i

    def process_spider_exception(self, response, exception, spider):
        # Called when a spider or process_spider_input() method
        # (from other spider middleware) raises an exception.

        # Should return either None or an iterable of Request or item objects.
        pass

    async def process_start(self, start):
        # Called with an async iterator over the spider start() method or the
        # maching method of an earlier spider middleware.
        async for item_or_request in start:
            yield item_or_request

    def spider_opened(self, spider):
        spider.logger.info("Spider opened: %s" % spider.name)


class NpkScraperDownloaderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the downloader middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_request(self, request, spider):
        # Called for each request that goes through the downloader
        # middleware.

        # Must either:
        # - return None: continue processing this request
        # - or return a Response object
        # - or return a Request object
        # - or raise IgnoreRequest: process_exception() methods of
        #   installed downloader middleware will be called
        return None

    def process_response(self, request, response, spider):
        # Called with the response returned from the downloader.

        # Must either;
        # - return a Response object
        # - return a Request object
        # - or raise IgnoreRequest
        return response

    def process_exception(self, request, exception, spider):
        # Called when a download handler or a process_request()
        # (from other downloader middleware) raises an exception.

        # Must either:
        # - return None: continue processing this exception
        # - return a Response object: stops process_exception() chain
        # - return a Request object: stops process_exception() chain
        pass

    def spider_opened(self, spider):
        spider.logger.info("Spider opened: %s" % spider.name)
