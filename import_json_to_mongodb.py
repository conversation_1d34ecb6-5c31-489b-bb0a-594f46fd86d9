#!/usr/bin/env python3
"""
Import existing JSON files to MongoDB.

This script scans the output/articles directory and imports all JSON files
to MongoDB, checking for duplicates based on article_id.
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, DuplicateKeyError
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    print("❌ PyMongo not installed. Install with: pip install pymongo")
    sys.exit(1)


class JSONToMongoImporter:
    """Import JSON files to MongoDB."""
    
    def __init__(self, mongo_uri="mongodb://localhost:27017/", 
                 database="legal_documents", collection="documents"):
        """Initialize importer."""
        self.mongo_uri = mongo_uri
        self.database_name = database
        self.collection_name = collection
        self.client = None
        self.db = None
        self.collection = None
        
        self.stats = {
            'files_processed': 0,
            'documents_inserted': 0,
            'documents_updated': 0,
            'duplicates_skipped': 0,
            'errors': 0
        }
        
    def connect(self):
        """Connect to MongoDB."""
        try:
            self.client = MongoClient(self.mongo_uri)
            self.db = self.client[self.database_name]
            self.collection = self.db[self.collection_name]
            
            # Test connection
            self.client.admin.command('ping')
            print(f"✅ Connected to MongoDB: {self.mongo_uri}")
            print(f"📁 Database: {self.database_name}, Collection: {self.collection_name}")
            
            # Create unique index on article_id
            self.collection.create_index("article_id", unique=True)
            print("📇 Ensured unique index on article_id")
            
            return True
            
        except ConnectionFailure as e:
            print(f"❌ Failed to connect to MongoDB: {e}")
            return False
            
    def close(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")
            
    def import_json_file(self, json_file_path):
        """Import a single JSON file."""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                article_data = json.load(f)
                
            article_id = article_data.get('article_id')
            if not article_id:
                print(f"⚠️ Skipping file {json_file_path}: missing article_id")
                self.stats['errors'] += 1
                return
                
            self.stats['files_processed'] += 1
            
            # Check if document already exists
            existing_doc = self.collection.find_one({"article_id": article_id})
            
            # Add timestamps
            now = datetime.utcnow().isoformat()
            article_data["updated_at"] = now
            
            if existing_doc:
                # Check if we should update
                if self.should_update(existing_doc, article_data):
                    self.collection.update_one(
                        {"article_id": article_id},
                        {"$set": article_data}
                    )
                    self.stats['documents_updated'] += 1
                    print(f"🔄 Updated: {article_id}")
                else:
                    self.stats['duplicates_skipped'] += 1
                    print(f"⏭️ Skipped: {article_id} (no changes)")
            else:
                # New document
                article_data["created_at"] = now
                try:
                    self.collection.insert_one(article_data)
                    self.stats['documents_inserted'] += 1
                    print(f"💾 Inserted: {article_id}")
                except DuplicateKeyError:
                    self.stats['duplicates_skipped'] += 1
                    print(f"⏭️ Duplicate: {article_id}")
                    
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in {json_file_path}: {e}")
            self.stats['errors'] += 1
        except Exception as e:
            print(f"❌ Error processing {json_file_path}: {e}")
            self.stats['errors'] += 1
            
    def should_update(self, existing_doc, new_data):
        """Check if document should be updated."""
        # Update if content was not previously extracted but now is
        if (not existing_doc.get('content_extracted', False) and 
            new_data.get('content_extracted', False)):
            return True
            
        # Update if new content is significantly longer
        existing_text_length = len(existing_doc.get('text', ''))
        new_text_length = len(new_data.get('text', ''))
        
        if new_text_length > existing_text_length * 1.1:  # 10% longer
            return True
            
        # Update if extraction info is newer
        existing_extracted_at = existing_doc.get('extraction_info', {}).get('extracted_at')
        new_extracted_at = new_data.get('extraction_info', {}).get('extracted_at')
        
        if existing_extracted_at and new_extracted_at:
            if new_extracted_at > existing_extracted_at:
                return True
                
        return False
        
    def import_directory(self, directory_path):
        """Import all JSON files from a directory."""
        directory = Path(directory_path)
        
        if not directory.exists():
            print(f"❌ Directory not found: {directory_path}")
            return
            
        print(f"📂 Scanning directory: {directory_path}")
        
        json_files = list(directory.rglob("*.json"))
        total_files = len(json_files)
        
        if total_files == 0:
            print("❌ No JSON files found")
            return
            
        print(f"📄 Found {total_files} JSON files")
        
        for i, json_file in enumerate(json_files, 1):
            if i % 10 == 0 or i == total_files:
                print(f"📊 Progress: {i}/{total_files} files processed")
                
            self.import_json_file(json_file)
            
    def print_stats(self):
        """Print import statistics."""
        print("\n📊 Import Statistics:")
        print(f"   Files processed: {self.stats['files_processed']}")
        print(f"   Documents inserted: {self.stats['documents_inserted']}")
        print(f"   Documents updated: {self.stats['documents_updated']}")
        print(f"   Duplicates skipped: {self.stats['duplicates_skipped']}")
        print(f"   Errors: {self.stats['errors']}")
        
        total_success = self.stats['documents_inserted'] + self.stats['documents_updated']
        print(f"   Total successful: {total_success}")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Import JSON files to MongoDB")
    parser.add_argument("--directory", default="output/articles", 
                       help="Directory containing JSON files")
    parser.add_argument("--uri", default="mongodb://localhost:27017/", 
                       help="MongoDB URI")
    parser.add_argument("--database", default="legal_documents", 
                       help="Database name")
    parser.add_argument("--collection", default="documents", 
                       help="Collection name")
    
    args = parser.parse_args()
    
    print("📥 JSON to MongoDB Importer")
    print("=" * 50)
    
    # Initialize importer
    importer = JSONToMongoImporter(args.uri, args.database, args.collection)
    
    if not importer.connect():
        sys.exit(1)
        
    try:
        # Import files
        importer.import_directory(args.directory)
        
        # Print statistics
        importer.print_stats()
        
        # Show collection stats
        total_docs = importer.collection.count_documents({})
        print(f"\n📊 Collection now contains {total_docs} documents")
        
        print("\n✅ Import completed!")
        
    finally:
        importer.close()


if __name__ == "__main__":
    main()
