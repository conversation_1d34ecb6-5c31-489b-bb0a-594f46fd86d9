#!/usr/bin/env python3
"""
Show scraping progress in a readable format.

This script reads the progress files and displays:
- Overall progress statistics
- Progress by year
- Progress by mudda type
- Pending combinations
"""

import json
import sys
from pathlib import Path
from datetime import datetime


def load_progress():
    """Load progress data from files."""
    progress_file = Path('output/url_collection_progress.json')
    detailed_file = Path('output/progress_by_combination.json')
    stats_file = Path('output/url_collection_stats.json')
    
    progress = {}
    detailed = {}
    stats = {}
    
    if progress_file.exists():
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress = json.load(f)
        except Exception as e:
            print(f"❌ Error loading progress file: {e}")
    
    if detailed_file.exists():
        try:
            with open(detailed_file, 'r', encoding='utf-8') as f:
                detailed = json.load(f)
        except Exception as e:
            print(f"❌ Error loading detailed progress file: {e}")
    
    if stats_file.exists():
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)
        except Exception as e:
            print(f"❌ Error loading stats file: {e}")
    
    return progress, detailed, stats


def show_overall_progress(detailed, stats):
    """Show overall progress statistics."""
    print("📊 Overall Progress")
    print("=" * 50)
    
    if detailed:
        total = detailed.get('total_combinations', 0)
        completed = detailed.get('completed_combinations', 0)
        percentage = detailed.get('progress_percentage', 0)
        last_updated = detailed.get('last_updated', 'Unknown')
        
        print(f"Total combinations: {total}")
        print(f"Completed combinations: {completed}")
        print(f"Pending combinations: {total - completed}")
        print(f"Progress: {percentage}%")
        print(f"Last updated: {last_updated}")
    
    if stats:
        total_articles = stats.get('total_articles', 0)
        start_time = stats.get('start_time', 'Unknown')
        
        print(f"Total articles collected: {total_articles}")
        print(f"Started: {start_time}")


def show_progress_by_year(detailed):
    """Show progress breakdown by year."""
    if not detailed or 'combinations' not in detailed:
        return
        
    print("\n📅 Progress by Year")
    print("=" * 50)
    
    combinations = detailed['combinations']
    
    for year in sorted(combinations.keys()):
        year_data = combinations[year]
        
        # Count completed and total for this year
        total_year = 0
        completed_year = 0
        
        for month_name, month_data in year_data.items():
            for mudda_type_name, combo_data in month_data.items():
                total_year += 1
                if combo_data.get('completed', False):
                    completed_year += 1
        
        percentage = round((completed_year / total_year) * 100, 2) if total_year > 0 else 0
        
        print(f"Year {year}: {completed_year}/{total_year} ({percentage}%)")


def show_progress_by_mudda_type(detailed):
    """Show progress breakdown by mudda type."""
    if not detailed or 'combinations' not in detailed:
        return
        
    print("\n⚖️ Progress by Mudda Type")
    print("=" * 50)
    
    combinations = detailed['combinations']
    mudda_type_stats = {}
    
    # Aggregate by mudda type across all years and months
    for year, year_data in combinations.items():
        for month_name, month_data in year_data.items():
            for mudda_type_name, combo_data in month_data.items():
                if mudda_type_name not in mudda_type_stats:
                    mudda_type_stats[mudda_type_name] = {'total': 0, 'completed': 0}
                
                mudda_type_stats[mudda_type_name]['total'] += 1
                if combo_data.get('completed', False):
                    mudda_type_stats[mudda_type_name]['completed'] += 1
    
    # Sort by completion percentage
    sorted_mudda_types = sorted(
        mudda_type_stats.items(),
        key=lambda x: x[1]['completed'] / x[1]['total'] if x[1]['total'] > 0 else 0,
        reverse=True
    )
    
    for mudda_type_name, stats in sorted_mudda_types:
        total = stats['total']
        completed = stats['completed']
        percentage = round((completed / total) * 100, 2) if total > 0 else 0
        
        print(f"{mudda_type_name}: {completed}/{total} ({percentage}%)")


def show_pending_combinations(detailed, limit=20):
    """Show pending combinations."""
    if not detailed or 'combinations' not in detailed:
        return
        
    print(f"\n⏳ Next {limit} Pending Combinations")
    print("=" * 50)
    
    combinations = detailed['combinations']
    pending = []
    
    # Collect all pending combinations
    for year, year_data in combinations.items():
        for month_name, month_data in year_data.items():
            for mudda_type_name, combo_data in month_data.items():
                if not combo_data.get('completed', False):
                    pending.append({
                        'year': year,
                        'month': month_name,
                        'mudda_type': mudda_type_name,
                        'combination_id': combo_data.get('combination_id', 'Unknown')
                    })
    
    # Sort by year, then show first N
    pending.sort(key=lambda x: (x['year'], x['month'], x['mudda_type']))
    
    if not pending:
        print("🎉 All combinations completed!")
        return
    
    for i, combo in enumerate(pending[:limit], 1):
        print(f"{i:2d}. {combo['year']} - {combo['month']} - {combo['mudda_type']}")
        print(f"     ID: {combo['combination_id']}")
    
    if len(pending) > limit:
        print(f"\n... and {len(pending) - limit} more pending combinations")


def show_recent_activity(progress):
    """Show recent activity from collected URLs."""
    if not progress or 'collected_urls' not in progress:
        return
        
    print("\n🕒 Recent Activity")
    print("=" * 50)
    
    collected_urls = progress['collected_urls']
    recent_articles = []
    
    # Collect recent articles with timestamps
    for year, year_data in collected_urls.items():
        for month_name, month_data in year_data.items():
            for mudda_type_name, articles in month_data.items():
                for article in articles:
                    if 'scraped_at' in article:
                        recent_articles.append({
                            'article_id': article.get('article_id', 'Unknown'),
                            'title': article.get('title', 'No title')[:60] + '...',
                            'scraped_at': article.get('scraped_at'),
                            'year': year,
                            'month': month_name,
                            'mudda_type': mudda_type_name
                        })
    
    # Sort by scraped_at timestamp and show last 10
    recent_articles.sort(key=lambda x: x['scraped_at'], reverse=True)
    
    for article in recent_articles[:10]:
        print(f"📄 {article['article_id']}: {article['title']}")
        print(f"    {article['year']} - {article['month']} - {article['mudda_type']}")
        print(f"    Scraped: {article['scraped_at']}")
        print()


def main():
    """Main function."""
    print("🔍 NPK Scraper Progress Report")
    print("=" * 60)
    
    # Load progress data
    progress, detailed, stats = load_progress()
    
    if not progress and not detailed and not stats:
        print("❌ No progress files found. Run the scraper first.")
        return
    
    # Show different views
    show_overall_progress(detailed, stats)
    show_progress_by_year(detailed)
    show_progress_by_mudda_type(detailed)
    show_pending_combinations(detailed)
    show_recent_activity(progress)
    
    print("\n💡 Commands:")
    print("   To continue scraping: scrapy crawl nkp_url_collector")
    print("   To check MongoDB: python mongodb_utils.py stats")
    print("   To view this report: python show_progress.py")


if __name__ == "__main__":
    main()
