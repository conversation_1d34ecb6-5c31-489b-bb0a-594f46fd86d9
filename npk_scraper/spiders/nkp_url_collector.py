import scrapy
import re
import json
import random
import time
import traceback
from urllib.parse import urljoin, urlencode
from datetime import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class SeleniumContentExtractor:
    """Extract article content using Selenium for JavaScript-heavy sites"""

    def __init__(self):
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        """Setup Chrome driver in headless mode"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("✅ Selenium Chrome driver initialized")
        except Exception as e:
            print(f"❌ Failed to initialize Selenium driver: {e}")
            traceback.print_exc()
            self.driver = None

    def extract_article_data(self, url):
        """Extract article data using Selenium"""
        if not self.driver:
            print("❌ Selenium driver not available")
            return None

        try:
            print(f"🌐 Loading URL with Selenium: {url}")
            self.driver.get(url)

            # Wait for page to load
            time.sleep(3)

            # Check for rate limiting
            page_source = self.driver.page_source
            if "Request Rejected" in page_source:
                print("❌ Rate limiting detected by Selenium")
                return None

            # Find article content
            try:
                article_element = self.driver.find_element(By.TAG_NAME, "article")
                article_html = article_element.get_attribute('innerHTML')
                print(f"✅ Found article element: {len(article_html)} chars")
            except:
                print("❌ No article element found, using full page")
                article_html = page_source

            # Extract text content
            text_content = self.extract_text_from_html(article_html)

            return {
                'metadata': {},
                'content': {
                    'text': text_content,
                    'complete_text': text_content,
                    'paragraphs': text_content.split('\n\n') if text_content else [],
                    'paragraph_count': len(text_content.split('\n\n')) if text_content else 0,
                    'html_content': article_html
                },
                'structured_content': {},
                'extraction_info': {
                    'extracted_at': datetime.now().isoformat(),
                    'content_length': len(text_content) if text_content else 0,
                    'method': 'selenium'
                }
            }

        except Exception as e:
            print(f"❌ Selenium extraction failed: {e}")
            traceback.print_exc()
            return None

    def extract_text_from_html(self, html_content):
        """Extract clean text from HTML with proper multiline formatting"""
        try:
            # Remove script and style elements
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

            # Convert <br> tags to \n before removing HTML tags
            html_content = re.sub(r'<br\s*/?>', '\n', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<br\s+[^>]*>', '\n', html_content, flags=re.IGNORECASE)

            # Convert </p> tags to \n\n for paragraph breaks
            html_content = re.sub(r'</p>', '\n\n', html_content, flags=re.IGNORECASE)

            # Convert </div> tags to \n for div breaks
            html_content = re.sub(r'</div>', '\n', html_content, flags=re.IGNORECASE)

            # Remove remaining HTML tags
            text = re.sub(r'<[^>]+>', '', html_content)

            # Clean up excessive whitespace but preserve line breaks
            text = re.sub(r'[ \t]+', ' ', text)  # Replace multiple spaces/tabs with single space
            text = re.sub(r'\n[ \t]+', '\n', text)  # Remove spaces at start of lines
            text = re.sub(r'[ \t]+\n', '\n', text)  # Remove spaces at end of lines
            text = re.sub(r'\n{3,}', '\n\n', text)  # Replace 3+ newlines with 2

            # Strip leading/trailing whitespace
            text = text.strip()

            return text
        except Exception as e:
            print(f"❌ Text extraction failed: {e}")
            return ""

    def close(self):
        """Close the Selenium driver"""
        if self.driver:
            try:
                self.driver.quit()
                print("✅ Selenium driver closed")
            except:
                pass


class ArticleContentExtractor:
    """Extract article content using regex patterns"""

    def __init__(self):
        # Regex patterns for article extraction
        self.patterns = {
            'article_container': r'<article class=" type-post format-standard hentry clearfix">(.*?)<!-- start of sidebar -->|<div class="col-md-4 para-sections">',
            'title': r'<h1 class="post-title"><a[^>]*>([^<]+)</a></h1>',
            'edition_info': r'<div id="edition-info">(.*?)</div>',
            'edition_part': r'भाग:\s*<strong>([^<]+)</strong>',
            'edition_year': r'साल:\s*<strong>([^<]+)</strong>',
            'edition_month': r'महिना:\s*<strong>([^<]+)</strong>',
            'edition_volume': r'अंक:\s*<strong>([^<]+)</strong>',
            'post_meta': r'<div class="post-meta clearfix">(.*?)</div>',
            'decision_date': r'फैसला मिति\s*:([^<\s]+)',
            'views_count': r'glyphicon-eye-open[^>]*></i>\s*([0-9]+)',
            'faisala_detail': r'<div id="faisala_detail[^"]*"[^>]*>(.*?)</div>',
            'paragraph_content': r'<p[^>]*>(.*?)</p>',
            'html_tags': r'<[^>]+>',
            'extra_spaces': r'\s+',
            'watermark': r'<img[^>]*watermark[^>]*>',
        }

    def extract_article_data(self, html_content):
        """Extract complete article data from HTML"""
        try:
            # Extract content between <article> and </article> tags
            article_pattern = r'<article[^>]*>(.*?)</article>'

            article_match = re.search(article_pattern, html_content, re.DOTALL | re.IGNORECASE)
            if not article_match:
                # Log why extraction failed
                if '<article' in html_content.lower():
                    print(f"❌ Found <article> tag but pattern didn't match")
                    # Show first article tag found
                    article_start = html_content.lower().find('<article')
                    if article_start >= 0:
                        article_snippet = html_content[article_start:article_start+200]
                        print(f"   Article tag snippet: {article_snippet}")
                else:
                    print(f"❌ No <article> tag found in HTML")
                    print(f"   HTML length: {len(html_content)} chars")
                    print(f"   HTML preview: {html_content[:500]}...")
                return None

            article_content = article_match.group(1)
            print(f"✅ Successfully extracted article content: {len(article_content)} chars")
        except Exception as e:
            print(f"❌ Exception in extract_article_data: {e}")
            traceback.print_exc()
            return None

        try:
            # Extract metadata
            metadata = self.extract_metadata(article_content)
            print(f"✅ Extracted metadata: {len(metadata)} fields")
        except Exception as e:
            print(f"❌ Failed to extract metadata: {e}")
            traceback.print_exc()
            metadata = {}

        try:
            # Extract full text
            full_text = self.extract_full_text(article_content)
            print(f"✅ Extracted text: {len(full_text.get('clean_text', ''))} chars")
        except Exception as e:
            print(f"❌ Failed to extract full text: {e}")
            traceback.print_exc()
            full_text = {}

        try:
            # Extract structured content
            structured_content = self.extract_structured_content(full_text.get('paragraphs', []))
            print(f"✅ Extracted structured content")
        except Exception as e:
            print(f"❌ Failed to extract structured content: {e}")
            traceback.print_exc()
            structured_content = {}

        # Get the clean text and complete text
        clean_text = full_text.get('clean_text', '')
        complete_text = full_text.get('complete_text', clean_text)  # Use clean_text as fallback

        return {
            'metadata': metadata,
            'content': {
                'text': clean_text,  # Complete formatted text with line breaks
                'complete_text': complete_text,  # Raw extracted text
                'paragraphs': full_text.get('paragraphs', []),
                'paragraph_count': full_text.get('paragraph_count', 0),
                'html_content': full_text.get('html_content', '')
            },
            'structured_content': structured_content,
            'extraction_info': {
                'extracted_at': datetime.now().isoformat(),
                'content_length': len(clean_text)
            }
        }

    def extract_metadata(self, article_html):
        """Extract metadata from article HTML"""
        metadata = {}

        # Extract title
        title_match = re.search(self.patterns['title'], article_html, re.DOTALL)
        if title_match:
            metadata['title'] = self.clean_text(title_match.group(1))

        # Extract edition info
        edition_match = re.search(self.patterns['edition_info'], article_html, re.DOTALL)
        if edition_match:
            edition_html = edition_match.group(1)
            metadata['edition'] = {
                'part': self.extract_pattern(edition_html, self.patterns['edition_part']),
                'year': self.extract_pattern(edition_html, self.patterns['edition_year']),
                'month': self.extract_pattern(edition_html, self.patterns['edition_month']),
                'volume': self.extract_pattern(edition_html, self.patterns['edition_volume'])
            }

        # Extract decision date and views
        meta_match = re.search(self.patterns['post_meta'], article_html, re.DOTALL)
        if meta_match:
            meta_html = meta_match.group(1)
            metadata['decision_date'] = self.extract_pattern(meta_html, self.patterns['decision_date'])
            metadata['views'] = self.extract_pattern(meta_html, self.patterns['views_count'])

        return metadata

    def extract_full_text(self, article_html):
        """Extract full text content with proper formatting"""
        # Use the entire article content since we already extracted just the article tag
        detail_html = article_html

        # Remove watermarks
        detail_html = re.sub(self.patterns['watermark'], '', detail_html)

        # Extract paragraphs
        paragraphs = re.findall(self.patterns['paragraph_content'], detail_html, re.DOTALL)
        clean_paragraphs = []

        for para in paragraphs:
            clean_para = self.clean_text(para)
            if clean_para.strip():
                clean_paragraphs.append(clean_para)

        # Also extract complete text from the entire detail section
        complete_text = self.clean_text(detail_html)

        # Join paragraphs with double line breaks for proper paragraph separation
        full_clean_text = '\n\n'.join(clean_paragraphs) if clean_paragraphs else complete_text

        return {
            'html_content': detail_html,
            'paragraphs': clean_paragraphs,
            'clean_text': full_clean_text,
            'complete_text': complete_text,  # Full formatted text from entire section
            'paragraph_count': len(clean_paragraphs)
        }

    def extract_structured_content(self, paragraphs):
        """Extract structured sections from paragraphs"""
        sections = {
            'court_info': [],
            'judges': [],
            'case_parties': [],
            'case_numbers': [],
            'legal_acts': [],
            'decision_text': []
        }

        for para in paragraphs:
            if not para.strip():
                continue

            if re.search(r'अदालत', para):
                sections['court_info'].append(para)
            if re.search(r'माननीय न्यायाधीश', para):
                sections['judges'].append(para)
            if re.search(r'पुनरावेदक|प्रत्यर्थी|वादी|प्रतिवादी', para):
                sections['case_parties'].append(para)
            if re.search(r'मुद्दा.*नं', para) and len(para) < 200:
                sections['case_numbers'].append(para)
            if re.search(r'ऐन.*२०', para) and len(para) < 200:
                sections['legal_acts'].append(para)
            if re.search(r'ठहर्छ|ठहर', para):
                sections['decision_text'].append(para)

        return sections

    def extract_pattern(self, text, pattern):
        """Extract text using regex pattern"""
        match = re.search(pattern, text, re.DOTALL)
        return self.clean_text(match.group(1)) if match else None

    def clean_text(self, text):
        """Clean HTML and normalize text with proper formatting"""
        if not text:
            return ""

        # Convert common HTML elements to text formatting
        # Convert <br> tags to line breaks
        text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)

        # Convert </p> tags to double line breaks (paragraph separation)
        text = re.sub(r'</p>', '\n\n', text, flags=re.IGNORECASE)

        # Convert </div> tags to line breaks
        text = re.sub(r'</div>', '\n', text, flags=re.IGNORECASE)

        # Convert </h1>, </h2>, etc. to line breaks
        text = re.sub(r'</h[1-6]>', '\n\n', text, flags=re.IGNORECASE)

        # Convert </li> to line breaks
        text = re.sub(r'</li>', '\n', text, flags=re.IGNORECASE)

        # Remove all remaining HTML tags
        text = re.sub(self.patterns['html_tags'], '', text)

        # Decode HTML entities
        text = text.replace('&nbsp;', ' ').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>').replace('&quot;', '"')

        # Clean up multiple consecutive spaces but preserve line breaks
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs to single space
        text = re.sub(r' *\n *', '\n', text)  # Remove spaces around line breaks
        text = re.sub(r'\n{3,}', '\n\n', text)  # Multiple line breaks to double

        return text.strip()


class NkpUrlCollectorSpider(scrapy.Spider):
    name = 'nkp_url_collector'
    allowed_domains = ['nkp.gov.np']

    # Anti-blocking settings
    custom_settings = {
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'DOWNLOAD_DELAY': 5,  # Increased delay
        'RANDOMIZE_DOWNLOAD_DELAY': 0.8,  # More randomization
        'AUTOTHROTTLE_ENABLED': True,
        'AUTOTHROTTLE_START_DELAY': 3,
        'AUTOTHROTTLE_MAX_DELAY': 15,
        'AUTOTHROTTLE_TARGET_CONCURRENCY': 0.5,  # More conservative
        'AUTOTHROTTLE_DEBUG': True,
        'COOKIES_ENABLED': True,
        'RETRY_ENABLED': True,
        'RETRY_TIMES': 3,
        'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
        'ROBOTSTXT_OBEY': False,
        'HTTPCACHE_ENABLED': True,
        'HTTPCACHE_EXPIRATION_SECS': 3600,
        'DEFAULT_REQUEST_HEADERS': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }
    }

    # User agent rotation
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize Selenium content extractor
        self.selenium_extractor = SeleniumContentExtractor()
        # Keep legacy extractor as fallback
        self.article_extractor = ArticleContentExtractor()

        # Configuration - Multiple years and all months
        self.YEARS = ["2075", "2076", "2077", "2078", "2079", "2080", "2081"]  # All available years
        self.BASE_URL = "https://nkp.gov.np/advance_search"

        # Month mapping: ID for search → Clean name for storage
        self.MONTHS = {
            "1": "बैशाख",
            "2": "जेष्ठ",
            "3": "असार",
            "4": "श्रावण",
            "5": "भाद्र",
            "6": "असोज",
            "7": "कार्तिक",
            "8": "मंसिर",
            "9": "पौस",
            "10": "माघ",
            "11": "फागुन",
            "12": "चैत्र"
        }

        # Mudda type mapping: ID for search → Clean name for storage
        self.MUDDA_TYPES = {
            "1": "दुनियाबादी_देवानी",
            "2": "सरकारबादी_देवानी",
            "3": "दुनियावादी_फौजदारी",
            "4": "सरकारवादी_फौजदारी",
            "5": "रिट",
            "6": "निवेदन",
            "7": "विविध"
        }
        
        # Data collection structure - Year → Month → Mudda Type
        self.collected_data = {}
        for year in self.YEARS:
            self.collected_data[year] = {}
        
        # Progress tracking
        self.progress_file = Path('output/url_collection_progress.json')
        self.output_file = Path('output/collected_articles_all_years.json')
        self.stats_file = Path('output/url_collection_stats.json')
        
        # Load existing progress
        self.progress = self.load_progress()
        
        # Statistics for all years
        months_count = len(self.MONTHS)  # All months (no empty month anymore)
        total_combinations = len(self.YEARS) * len(self.MUDDA_TYPES) * months_count
        self.stats = {
            'total_years': len(self.YEARS),
            'total_combinations': total_combinations,
            'completed_combinations': 0,
            'total_articles': 0,
            'start_time': datetime.now().isoformat()
        }

    def load_progress(self):
        """Load existing progress"""
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Could not load progress: {e}")
        
        return {
            'completed_combinations': [],
            'collected_urls': {}
        }

    def save_progress(self):
        """Save current progress"""
        try:
            self.progress_file.parent.mkdir(exist_ok=True)
            self.progress['collected_urls'] = self.collected_data
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save progress: {e}")

    def save_stats(self):
        """Save current statistics"""
        try:
            self.stats_file.parent.mkdir(exist_ok=True)
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save stats: {e}")

    def get_random_user_agent(self):
        """Get a random user agent"""
        return random.choice(self.user_agents)

    def add_random_delay(self):
        """Add random delay between requests - balanced for speed"""
        delay = random.uniform(1, 3)  # 1-3 seconds delay
        self.logger.debug(f"Adding delay of {delay:.1f} seconds...")
        time.sleep(delay)

    def get_request_headers(self):
        """Get headers that work with the site (based on working curl)"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Referer': 'https://nkp.gov.np/',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i'
        }
        return headers

    def save_individual_article(self, article_data, year, month_name, mudda_type_name):
        """Save individual article as JSON file with proper text formatting"""
        try:
            # Create folder structure: output/articles/2080/बैशाख/दुनियाबादी_देवानी/
            folder_path = Path(f'output/articles/{year}/{month_name}/{mudda_type_name}')
            folder_path.mkdir(parents=True, exist_ok=True)

            # Create file path: articleid.json
            article_id = article_data.get('article_id', 'unknown')
            file_path = folder_path / f'{article_id}.json'

            # Create a copy for formatting
            formatted_data = article_data.copy()

            # Format the text field with proper multiline structure
            text_content = formatted_data.get('text', '')
            if text_content and '\n' in text_content:
                # Keep the text as-is but ensure it's properly formatted for JSON
                # The \n characters will be preserved in the JSON
                formatted_data['text'] = text_content

            # Save article data with proper formatting
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(formatted_data, f, ensure_ascii=False, indent=2)

            # Return relative file path for metadata
            return str(file_path)

        except Exception as e:
            self.logger.error(f"Failed to save individual article {article_data.get('article_id')}: {e}")
            return None

    def start_requests(self):
        """Generate all search combinations for all years"""
        self.logger.info(f"Starting URL collection for years: {', '.join(self.YEARS)}")
        self.logger.info(f"Total combinations to process: {self.stats['total_combinations']}")

        for year in self.YEARS:
            for mudda_type_id, mudda_type_name in self.MUDDA_TYPES.items():
                for month_id, month_name in self.MONTHS.items():
                    combination_id = f"{year}_{mudda_type_id}_{month_id}"
                
                    # Skip if combination already completed
                    if combination_id in self.progress.get('completed_combinations', []):
                        self.logger.info(f"Skipping completed combination: {combination_id}")
                        continue

                    # Build search URL
                    search_params = {
                        'mudda_type': mudda_type_id,
                        'year': year,
                        'month': month_id,
                        'Submit': 'खोज्‍नुहोस्'
                    }

                    search_url = f"https://nkp.gov.np/advance_search?{urlencode(search_params)}"

                    self.logger.info(f"Processing: {mudda_type_name} - {month_name}")

                    yield scrapy.Request(
                        url=search_url,
                        callback=self.parse_search_results,
                        meta={
                            'year': year,
                            'mudda_type_id': mudda_type_id,
                            'mudda_type_name': mudda_type_name,
                            'month_id': month_id,
                            'month_name': month_name,
                            'combination_id': combination_id,
                            'page_num': 1
                        },
                        headers=self.get_request_headers(),
                        dont_filter=True,
                        errback=self.handle_error
                    )

    def handle_error(self, failure):
        """Handle request errors"""
        self.logger.error(f"Request failed: {failure.request.url} - {failure.value}")

    def parse_search_results(self, response):
        """Parse search results and extract article URLs"""
        year = response.meta['year']
        mudda_type_name = response.meta['mudda_type_name']
        month_name = response.meta['month_name']
        combination_id = response.meta['combination_id']
        page_num = response.meta['page_num']
        
        self.logger.info(f"Parsing page {page_num} for {mudda_type_name} - {month_name}")
        
        # Initialize data structure if needed - Year → Month → Mudda Type
        if month_name not in self.collected_data[year]:
            self.collected_data[year][month_name] = {}
        if mudda_type_name not in self.collected_data[year][month_name]:
            self.collected_data[year][month_name][mudda_type_name] = []
        
        # Extract detailed article information from main-listing section
        articles_data = []
        main_listing = response.css('div.main-listing')

        if main_listing:
            # Extract all articles with complete information
            articles = main_listing.css('article.format-standard.type-post.hentry.clearfix')

            for article in articles:
                # Extract URL and title from h3 link
                title_link = article.css('h3.post-title a[href*="/full_detail/"]')
                if not title_link:
                    continue

                href = title_link.css('::attr(href)').get()
                title = title_link.css('::text').get()

                if not href or not title:
                    continue

                full_url = urljoin(response.url, href)

                # Extract article ID from URL
                article_id = None
                id_match = re.search(r'/full_detail/(\d+)', href)
                if id_match:
                    article_id = id_match.group(1)

                # Extract decision date
                decision_date = None
                date_span = article.css('div.type-details span:contains("फैसला मिति")')
                if date_span:
                    date_text = date_span.css('::text').getall()
                    for text in date_text:
                        if 'फैसला मिति:' in text:
                            decision_date = text.replace('फैसला मिति:', '').strip()
                            break

                # Extract court type
                court_type = None
                court_link = article.css('div.type-details span a::text').get()
                if court_link:
                    court_type = court_link.strip()

                # Extract views count
                views = None
                views_span = article.css('div.type-details span.last')
                if views_span:
                    views_text = views_span.css('::text').get()
                    if views_text:
                        views = views_text.strip()

                # Extract parties information
                parties_info = {}
                parties_div = article.css('div.type-details:contains("पुनरावेदक")')
                if parties_div:
                    parties_text = parties_div.css('::text').getall()
                    full_parties_text = ' '.join([t.strip() for t in parties_text if t.strip()])

                    # Split by "बिरुद्ध" to get plaintiff and defendant
                    if 'बिरुद्ध' in full_parties_text:
                        parts = full_parties_text.split('बिरुद्ध')
                        if len(parts) >= 2:
                            plaintiff_part = parts[0].strip()
                            defendant_part = parts[1].strip()

                            # Extract plaintiff
                            if 'पुनरावेदक/ प्रतिवादी :' in plaintiff_part:
                                parties_info['plaintiff'] = plaintiff_part.replace('पुनरावेदक/ प्रतिवादी :', '').strip()

                            # Extract defendant
                            if 'विपक्षी/वादी :' in defendant_part:
                                parties_info['defendant'] = defendant_part.replace('विपक्षी/वादी :', '').strip()

                # Extract summary text
                summary = None
                summary_p = article.css('p::text').get()
                if summary_p:
                    summary = summary_p.strip()

                # Create article metadata object
                article_metadata = {
                    'article_id': article_id,
                    'url': full_url,
                    'title': title.strip(),
                    'decision_date': decision_date,
                    'court_type': court_type,
                    'views': views,
                    'parties': parties_info,
                    'summary': summary,
                    'page': page_num,
                    'mudda_type': mudda_type_name,
                    'month': month_name,
                    'scraped_at': datetime.now().isoformat(),
                    'content_extracted': False  # Will be updated after content extraction
                }

                # Check for duplicates based on article_id
                existing_ids = [item.get('article_id') for item in articles_data]
                if article_id not in existing_ids:
                    # Add to articles_data for now
                    articles_data.append(article_metadata)

                    # Request article content immediately with proper headers and cookies
                    self.logger.info(f"Requesting content for article {article_id}: {full_url}")

                    # Add cookies to the request
                    cookies = {
                        'f5_cspm': '1234',
                        'court_session': 'a%3A5%3A%7Bs%3A10%3A%22SESSION_ID%22%3Bs%3A32%3A%22ae49619f121dedcc68f7ecc41b42fdc4%22%3Bs%3A10%3A%22IP_ADDRESS%22%3Bs%3A15%3A%22103.180.241.192%22%3Bs%3A10%3A%22USER_AGENT%22%3Bs%3A70%3A%22Mozilla%2F5.0+%28X11%3B+Linux+x86_64%3B+rv%3A140.0%29+Gecko%2F20100101+Firefox%2F140.0%22%3Bs%3A13%3A%22LAST_ACTIVITY%22%3Bi%3A1753852405%3Bs%3A9%3A%22USER_DATA%22%3Bs%3A0%3A%22%22%3B%7D1d0e38d8e54bb251fc501cc436b453a7'
                    }

                    yield scrapy.Request(
                        url=full_url,
                        callback=self.parse_article_content,
                        meta={
                            'article_metadata': article_metadata,
                            'mudda_type_name': mudda_type_name,
                            'month_name': month_name,
                            'year': year,
                            'articles_data': articles_data,  # Pass the list to update
                            'articles_index': len(articles_data) - 1  # Index to update
                        },
                        headers=self.get_request_headers(),
                        cookies=cookies,
                        dont_filter=True,
                        errback=self.handle_error,
                        priority=100  # High priority for content extraction
                    )
            
            # Extract results count
            results_header = main_listing.css('h2::text').get()
            if results_header:
                count_match = re.search(r'(\d+)\s*खोजी नतिजाहरु', results_header)
                if count_match:
                    total_results = int(count_match.group(1))
                    self.logger.info(f"Found {len(articles_data)} articles on page {page_num} (Total: {total_results})")

        # Add article data to collection - Year → Month → Mudda Type
        self.collected_data[year][month_name][mudda_type_name].extend(articles_data)
        self.stats['total_articles'] += len(articles_data)
        
        # Check for pagination and follow next pages
        pagination_div = response.css('div#pagination')
        if pagination_div:
            # Look for next page links
            pagination_links = pagination_div.css('a[href*="per_page"]')
            next_page_found = False
            
            for link in pagination_links:
                href = link.css('::attr(href)').get()
                text = link.css('::text').get()
                
                if href and text and ('»' in text or text.strip().isdigit()):
                    # Check if this is a next page (higher per_page value)
                    if 'per_page=' in href:
                        try:
                            per_page_match = re.search(r'per_page=(\d+)', href)
                            if per_page_match:
                                per_page = int(per_page_match.group(1))
                                expected_next_page = page_num * 20  # Assuming 20 items per page
                                
                                if per_page >= expected_next_page:
                                    next_url = urljoin(response.url, href)
                                    self.logger.info(f"Following pagination to page {page_num + 1}: {next_url}")
                                    
                                    yield scrapy.Request(
                                        url=next_url,
                                        callback=self.parse_search_results,
                                        meta={
                                            **response.meta,
                                            'page_num': page_num + 1
                                        },
                                        dont_filter=True,
                                        errback=self.handle_error
                                    )
                                    next_page_found = True
                                    break
                        except ValueError:
                            continue
            
            if not next_page_found:
                # Mark combination as completed
                self.mark_combination_completed(combination_id)
        else:
            # No pagination, mark as completed
            self.mark_combination_completed(combination_id)

    def parse_article_content(self, response):
        """Parse individual article content and extract full text"""
        article_metadata = response.meta['article_metadata']
        mudda_type_name = response.meta['mudda_type_name']
        month_name = response.meta['month_name']
        year = response.meta.get('year', '2080')
        articles_data = response.meta.get('articles_data', [])
        articles_index = response.meta.get('articles_index', -1)

        article_id = article_metadata['article_id']
        self.logger.info(f"Extracting content for article: {article_id}")
        self.logger.info(f"Response URL: {response.url}")
        self.logger.info(f"Response status: {response.status}")
        self.logger.info(f"HTML content length: {len(response.text)} chars")

        # Check for rate limiting
        if "Request Rejected" in response.text or len(response.text) < 1000:
            self.logger.warning(f"⚠️ Rate limiting detected for {article_id}")
            self.logger.warning(f"Response preview: {response.text[:200]}...")
            # Add extra delay
            time.sleep(5)  # Wait 5 seconds before continuing

        try:
            # Use Selenium for content extraction
            self.logger.info(f"🌐 Using Selenium to extract content for {article_id}")
            extracted_data = self.selenium_extractor.extract_article_data(response.url)

            if extracted_data:
                self.logger.info(f"✅ Selenium content extraction successful for {article_id}")
            else:
                self.logger.warning(f"❌ Selenium extraction failed, trying fallback for {article_id}")
                # Fallback to regex-based extraction
                extracted_data = self.article_extractor.extract_article_data(response.text)
                if extracted_data:
                    self.logger.info(f"✅ Fallback extraction successful for {article_id}")
                else:
                    self.logger.warning(f"❌ Both extraction methods failed for {article_id}")
        except Exception as e:
            self.logger.error(f"❌ Exception during content extraction for {article_id}: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            extracted_data = None

        if extracted_data:
            # Get the extracted content
            content = extracted_data.get('content', {})
            text = content.get('text', '')
            complete_text = content.get('complete_text', '')

            # Update the article metadata with the required structure
            article_metadata.update({
                'content_extracted': True,
                'text': text,  # Complete formatted text with line breaks
                'actual_cleaned_text': complete_text,  # Raw extracted text
                'metadata': {
                    'decision_date': article_metadata.get('decision_date', ''),
                    'views': article_metadata.get('views', ''),
                    'court_type': article_metadata.get('court_type', ''),
                    'parties': article_metadata.get('parties', {}),
                    'extraction_info': extracted_data.get('extraction_info', {}),
                    **extracted_data.get('metadata', {})  # Include any additional metadata
                },
                'structured_content': extracted_data.get('structured_content', {}),
                'extraction_info': extracted_data.get('extraction_info', {})
            })

            self.logger.info(f"Successfully extracted content for article {article_metadata['article_id']} - {len(text)} characters")
        else:
            self.logger.warning(f"Failed to extract content for article: {article_metadata['article_id']}")
            article_metadata.update({
                'content_extracted': False,
                'text': '',
                'actual_cleaned_text': '',
                'extraction_error': 'Failed to parse article content',
                'metadata': {
                    'decision_date': article_metadata.get('decision_date', ''),
                    'views': article_metadata.get('views', ''),
                    'court_type': article_metadata.get('court_type', ''),
                    'parties': article_metadata.get('parties', {})
                }
            })

        # 1. Save individual article file in hierarchy structure
        file_path = self.save_individual_article(article_metadata, year, month_name, mudda_type_name)
        if file_path:
            article_metadata['file_path'] = file_path
            self.logger.info(f"✅ Saved individual article file: {file_path}")
        else:
            self.logger.error(f"❌ Failed to save individual article file for {article_id}")

        # 2. Update the article in main collected_data structure
        if year in self.collected_data:
            if month_name in self.collected_data[year]:
                if mudda_type_name in self.collected_data[year][month_name]:
                    # Find and update the article
                    for i, article in enumerate(self.collected_data[year][month_name][mudda_type_name]):
                        if article.get('article_id') == article_metadata['article_id']:
                            self.collected_data[year][month_name][mudda_type_name][i] = article_metadata
                            self.logger.info(f"✅ Updated article in main data structure: {article_id}")
                            break

        # 3. Update the articles_data list if available
        if articles_data and 0 <= articles_index < len(articles_data):
            articles_data[articles_index] = article_metadata
            self.logger.info(f"✅ Updated article in articles_data list: {article_id}")

        # 4. Save progress immediately
        self.save_progress()

        self.logger.info(f"🎉 Completed processing article {article_id} with {len(article_metadata.get('text', ''))} chars")

        # Return the item for pipeline processing
        return article_metadata

    def closed(self, reason):
        """Called when spider closes - cleanup Selenium driver"""
        self.logger.info(f"Spider closing: {reason}")
        if hasattr(self, 'selenium_extractor') and self.selenium_extractor:
            self.selenium_extractor.close()
        self.logger.info("Spider cleanup completed")

    def mark_combination_completed(self, combination_id):
        """Mark a combination as completed"""
        if combination_id not in self.progress.get('completed_combinations', []):
            self.progress.setdefault('completed_combinations', []).append(combination_id)
            self.stats['completed_combinations'] += 1
            self.save_progress()
            self.save_stats()
            self.logger.info(f"Completed combination: {combination_id} ({self.stats['completed_combinations']}/{self.stats['total_combinations']})")

    def closed(self, reason):
        """Called when spider is closed"""
        self.stats['end_time'] = datetime.now().isoformat()
        self.stats['reason'] = reason
        
        # Save final results
        self.save_progress()
        self.save_stats()
        
        # Save final output
        try:
            self.output_file.parent.mkdir(exist_ok=True)
            # Create summary data for main output (without full content)
            summary_data = {}
            for year, months in self.collected_data.items():
                summary_data[year] = {}
                for month, mudda_types in months.items():
                    summary_data[year][month] = {}
                    for mudda_type, articles in mudda_types.items():
                        summary_data[year][month][mudda_type] = []
                        for article in articles:
                            # Create summary without full content
                            summary_article = {
                                'article_id': article.get('article_id'),
                                'title': article.get('title'),
                                'url': article.get('url'),
                                'file_path': article.get('file_path'),
                                'decision_date': article.get('decision_date'),
                                'views': article.get('views'),
                                'content_extracted': article.get('content_extracted', False),
                                'content_length': article.get('extraction_info', {}).get('content_length', 0),
                                'scraped_at': article.get('scraped_at')
                            }
                            summary_data[year][month][mudda_type].append(summary_article)

            final_output = {
                'collection_info': {
                    'years': self.YEARS,
                    'total_years': len(self.YEARS),
                    'total_combinations': self.stats['total_combinations'],
                    'completed_combinations': self.stats['completed_combinations'],
                    'total_articles': self.stats['total_articles'],
                    'scraped_at': datetime.now().isoformat(),
                    'spider_name': self.name,
                    'hierarchy': 'Year → Month → Mudda Type',
                    'individual_files': 'output/articles/{year}/{month}/{mudda_type}/{article_id}.json'
                },
                'data': summary_data,
                'stats': self.stats
            }
            
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(final_output, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Saved final results to {self.output_file}")
        except Exception as e:
            self.logger.error(f"Could not save final output: {e}")
        
        # Log final statistics
        self.logger.info(f"Spider closed. Reason: {reason}")
        self.logger.info(f"Completed combinations: {self.stats['completed_combinations']}/{self.stats['total_combinations']}")
        self.logger.info(f"Total articles collected: {self.stats['total_articles']}")
