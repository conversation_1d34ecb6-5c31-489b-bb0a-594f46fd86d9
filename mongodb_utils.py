#!/usr/bin/env python3
"""
MongoDB utilities for managing the legal documents collection.

This script provides utilities to:
- Check collection statistics
- Query documents by article_id
- Find duplicates
- Export data
- Clean up the collection
"""

import argparse
import json
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure


class MongoDBManager:
    """Manager class for MongoDB operations."""
    
    def __init__(self, uri: str = "mongodb://localhost:27017/", 
                 database: str = "legal_documents", 
                 collection: str = "documents"):
        """Initialize MongoDB manager."""
        self.uri = uri
        self.database_name = database
        self.collection_name = collection
        self.client = None
        self.db = None
        self.collection = None
        
    def connect(self):
        """Connect to MongoDB."""
        try:
            self.client = MongoClient(self.uri)
            self.db = self.client[self.database_name]
            self.collection = self.db[self.collection_name]
            
            # Test connection
            self.client.admin.command('ping')
            print(f"✅ Connected to MongoDB: {self.uri}")
            print(f"📁 Database: {self.database_name}, Collection: {self.collection_name}")
            return True
            
        except ConnectionFailure as e:
            print(f"❌ Failed to connect to MongoDB: {e}")
            return False
            
    def close(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")
            
    def get_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        if not self.collection:
            return {}
            
        total_docs = self.collection.count_documents({})
        
        # Count by year
        year_pipeline = [
            {"$group": {"_id": "$year", "count": {"$sum": 1}}},
            {"$sort": {"_id": 1}}
        ]
        year_counts = list(self.collection.aggregate(year_pipeline))
        
        # Count by mudda_type
        mudda_pipeline = [
            {"$group": {"_id": "$mudda_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        mudda_counts = list(self.collection.aggregate(mudda_pipeline))
        
        # Count extracted vs not extracted
        extracted_count = self.collection.count_documents({"content_extracted": True})
        
        # Count by month for current year
        month_pipeline = [
            {"$group": {"_id": {"year": "$year", "month": "$month"}, "count": {"$sum": 1}}},
            {"$sort": {"_id.year": 1, "_id.month": 1}}
        ]
        month_counts = list(self.collection.aggregate(month_pipeline))
        
        return {
            "total_documents": total_docs,
            "extracted_content": extracted_count,
            "not_extracted": total_docs - extracted_count,
            "by_year": {str(item["_id"]): item["count"] for item in year_counts if item["_id"]},
            "by_mudda_type": {str(item["_id"]): item["count"] for item in mudda_counts if item["_id"]},
            "by_month": [{"year": item["_id"]["year"], "month": item["_id"]["month"], "count": item["count"]} 
                        for item in month_counts if item["_id"]["year"] and item["_id"]["month"]]
        }
        
    def print_stats(self):
        """Print formatted collection statistics."""
        stats = self.get_stats()
        
        print("\n📊 Collection Statistics:")
        print(f"   Total documents: {stats['total_documents']:,}")
        print(f"   Content extracted: {stats['extracted_content']:,}")
        print(f"   Not extracted: {stats['not_extracted']:,}")
        
        if stats['by_year']:
            print("\n📅 Documents by Year:")
            for year, count in stats['by_year'].items():
                print(f"   {year}: {count:,}")
                
        if stats['by_mudda_type']:
            print("\n⚖️ Documents by Mudda Type:")
            for mudda_type, count in list(stats['by_mudda_type'].items())[:10]:  # Top 10
                print(f"   {mudda_type}: {count:,}")
                
    def find_article(self, article_id: str) -> Optional[Dict[str, Any]]:
        """Find article by article_id."""
        if not self.collection:
            return None
            
        return self.collection.find_one({"article_id": article_id})
        
    def check_duplicates(self) -> List[Dict[str, Any]]:
        """Find duplicate article_ids."""
        if not self.collection:
            return []
            
        pipeline = [
            {"$group": {"_id": "$article_id", "count": {"$sum": 1}, "docs": {"$push": "$$ROOT"}}},
            {"$match": {"count": {"$gt": 1}}}
        ]
        
        return list(self.collection.aggregate(pipeline))
        
    def export_articles(self, query: Dict[str, Any] = None, 
                       output_file: str = None) -> List[Dict[str, Any]]:
        """Export articles matching query."""
        if not self.collection:
            return []
            
        if query is None:
            query = {}
            
        articles = list(self.collection.find(query))
        
        # Convert ObjectId to string for JSON serialization
        for article in articles:
            if '_id' in article:
                article['_id'] = str(article['_id'])
                
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            print(f"💾 Exported {len(articles)} articles to {output_file}")
            
        return articles
        
    def clean_collection(self, dry_run: bool = True):
        """Clean up collection by removing incomplete documents."""
        if not self.collection:
            return
            
        # Find documents without essential fields
        incomplete_query = {
            "$or": [
                {"article_id": {"$exists": False}},
                {"article_id": ""},
                {"title": {"$exists": False}},
                {"year": {"$exists": False}}
            ]
        }
        
        incomplete_docs = list(self.collection.find(incomplete_query))
        
        if dry_run:
            print(f"🧹 Found {len(incomplete_docs)} incomplete documents (dry run)")
            for doc in incomplete_docs[:5]:  # Show first 5
                print(f"   - {doc.get('article_id', 'NO_ID')}: {doc.get('title', 'NO_TITLE')[:50]}")
        else:
            result = self.collection.delete_many(incomplete_query)
            print(f"🗑️ Deleted {result.deleted_count} incomplete documents")
            
    def get_recent_articles(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most recently added articles."""
        if not self.collection:
            return []
            
        return list(self.collection.find().sort("created_at", -1).limit(limit))


def main():
    """Main function for command line interface."""
    parser = argparse.ArgumentParser(description="MongoDB utilities for legal documents")
    parser.add_argument("--uri", default="mongodb://localhost:27017/", 
                       help="MongoDB URI")
    parser.add_argument("--database", default="legal_documents", 
                       help="Database name")
    parser.add_argument("--collection", default="documents", 
                       help="Collection name")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Stats command
    subparsers.add_parser("stats", help="Show collection statistics")
    
    # Find command
    find_parser = subparsers.add_parser("find", help="Find article by ID")
    find_parser.add_argument("article_id", help="Article ID to find")
    
    # Duplicates command
    subparsers.add_parser("duplicates", help="Find duplicate articles")
    
    # Export command
    export_parser = subparsers.add_parser("export", help="Export articles")
    export_parser.add_argument("--output", help="Output file path")
    export_parser.add_argument("--year", help="Filter by year")
    export_parser.add_argument("--mudda-type", help="Filter by mudda type")
    
    # Clean command
    clean_parser = subparsers.add_parser("clean", help="Clean incomplete documents")
    clean_parser.add_argument("--execute", action="store_true", 
                             help="Actually delete documents (default is dry run)")
    
    # Recent command
    recent_parser = subparsers.add_parser("recent", help="Show recent articles")
    recent_parser.add_argument("--limit", type=int, default=10, 
                              help="Number of articles to show")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    # Initialize manager
    manager = MongoDBManager(args.uri, args.database, args.collection)
    
    if not manager.connect():
        sys.exit(1)
        
    try:
        if args.command == "stats":
            manager.print_stats()
            
        elif args.command == "find":
            article = manager.find_article(args.article_id)
            if article:
                print(f"\n📄 Article {args.article_id}:")
                print(f"   Title: {article.get('title', 'N/A')}")
                print(f"   Year: {article.get('year', 'N/A')}")
                print(f"   Month: {article.get('month', 'N/A')}")
                print(f"   Mudda Type: {article.get('mudda_type', 'N/A')}")
                print(f"   Content Extracted: {article.get('content_extracted', False)}")
                print(f"   Created: {article.get('created_at', 'N/A')}")
            else:
                print(f"❌ Article {args.article_id} not found")
                
        elif args.command == "duplicates":
            duplicates = manager.check_duplicates()
            if duplicates:
                print(f"⚠️ Found {len(duplicates)} duplicate article IDs:")
                for dup in duplicates:
                    print(f"   {dup['_id']}: {dup['count']} copies")
            else:
                print("✅ No duplicates found")
                
        elif args.command == "export":
            query = {}
            if args.year:
                query["year"] = args.year
            if args.mudda_type:
                query["mudda_type"] = args.mudda_type
                
            articles = manager.export_articles(query, args.output)
            if not args.output:
                print(f"📋 Found {len(articles)} articles matching query")
                
        elif args.command == "clean":
            manager.clean_collection(dry_run=not args.execute)
            
        elif args.command == "recent":
            articles = manager.get_recent_articles(args.limit)
            print(f"\n📰 {len(articles)} Most Recent Articles:")
            for article in articles:
                print(f"   {article.get('article_id', 'N/A')}: {article.get('title', 'N/A')[:60]}...")
                
    finally:
        manager.close()


if __name__ == "__main__":
    main()
