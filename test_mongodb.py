#!/usr/bin/env python3
"""
Test MongoDB connection and demonstrate the pipeline functionality.
"""

import json
import os
from datetime import datetime
from pathlib import Path

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    print("❌ PyMongo not installed. Install with: pip install pymongo")


def test_mongodb_connection():
    """Test MongoDB connection."""
    if not PYMONGO_AVAILABLE:
        return False
        
    try:
        client = MongoClient("mongodb://localhost:27017/")
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        # Test database and collection
        db = client["legal_documents"]
        collection = db["documents"]
        
        # Insert a test document
        test_doc = {
            "article_id": "test_123",
            "title": "Test Article",
            "year": "2080",
            "month": "बैशाख",
            "mudda_type": "दुनियाबादी_देवानी",
            "content_extracted": True,
            "created_at": datetime.utcnow().isoformat(),
            "test_document": True
        }
        
        # Check if test document already exists
        existing = collection.find_one({"article_id": "test_123"})
        if existing:
            print("🔄 Test document already exists, updating...")
            collection.update_one(
                {"article_id": "test_123"},
                {"$set": {"updated_at": datetime.utcnow().isoformat()}}
            )
        else:
            print("💾 Inserting test document...")
            collection.insert_one(test_doc)
            
        # Get collection stats
        total_docs = collection.count_documents({})
        test_docs = collection.count_documents({"test_document": True})
        
        print(f"📊 Collection stats:")
        print(f"   Total documents: {total_docs}")
        print(f"   Test documents: {test_docs}")
        
        # Clean up test document
        collection.delete_many({"test_document": True})
        print("🧹 Cleaned up test documents")
        
        client.close()
        return True
        
    except ConnectionFailure as e:
        print(f"❌ MongoDB connection failed: {e}")
        print("💡 Make sure MongoDB is running: sudo systemctl start mongod")
        return False
    except Exception as e:
        print(f"❌ Error testing MongoDB: {e}")
        return False


def load_sample_json_and_insert():
    """Load a sample JSON file and insert it into MongoDB."""
    if not PYMONGO_AVAILABLE:
        return
        
    # Find a sample JSON file
    articles_dir = Path("output/articles")
    sample_file = None
    
    if articles_dir.exists():
        for json_file in articles_dir.rglob("*.json"):
            sample_file = json_file
            break
            
    if not sample_file:
        print("❌ No sample JSON files found in output/articles/")
        return
        
    print(f"📄 Loading sample file: {sample_file}")
    
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            article_data = json.load(f)
            
        print(f"✅ Loaded article: {article_data.get('article_id', 'Unknown ID')}")
        print(f"   Title: {article_data.get('title', 'No title')[:60]}...")
        
        # Connect to MongoDB
        client = MongoClient("mongodb://localhost:27017/")
        db = client["legal_documents"]
        collection = db["documents"]
        
        article_id = article_data.get('article_id')
        if not article_id:
            print("❌ Article missing article_id")
            return
            
        # Check for duplicate
        existing = collection.find_one({"article_id": article_id})
        
        if existing:
            print(f"⏭️ Article {article_id} already exists in MongoDB")
        else:
            # Add MongoDB timestamps
            article_data["created_at"] = datetime.utcnow().isoformat()
            article_data["updated_at"] = datetime.utcnow().isoformat()
            
            # Insert the document
            collection.insert_one(article_data)
            print(f"💾 Inserted article {article_id} into MongoDB")
            
        # Show collection stats
        total_docs = collection.count_documents({})
        print(f"📊 Total documents in collection: {total_docs}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ Error processing sample file: {e}")


def show_collection_stats():
    """Show MongoDB collection statistics."""
    if not PYMONGO_AVAILABLE:
        return
        
    try:
        client = MongoClient("mongodb://localhost:27017/")
        db = client["legal_documents"]
        collection = db["documents"]
        
        total_docs = collection.count_documents({})
        print(f"\n📊 MongoDB Collection Statistics:")
        print(f"   Total documents: {total_docs}")
        
        if total_docs > 0:
            # Count by year
            year_pipeline = [
                {"$group": {"_id": "$year", "count": {"$sum": 1}}},
                {"$sort": {"_id": 1}}
            ]
            year_counts = list(collection.aggregate(year_pipeline))
            
            if year_counts:
                print(f"   By year:")
                for item in year_counts:
                    if item["_id"]:
                        print(f"     {item['_id']}: {item['count']}")
                        
            # Count extracted vs not extracted
            extracted = collection.count_documents({"content_extracted": True})
            not_extracted = collection.count_documents({"content_extracted": False})
            
            print(f"   Content extracted: {extracted}")
            print(f"   Content not extracted: {not_extracted}")
            
            # Show recent documents
            recent_docs = list(collection.find().sort("created_at", -1).limit(3))
            print(f"   Recent articles:")
            for doc in recent_docs:
                title = doc.get('title', 'No title')[:50]
                print(f"     {doc.get('article_id', 'No ID')}: {title}...")
                
        client.close()
        
    except Exception as e:
        print(f"❌ Error getting collection stats: {e}")


def main():
    """Main function."""
    print("🧪 Testing MongoDB Integration")
    print("=" * 50)
    
    # Test connection
    if test_mongodb_connection():
        print("\n📄 Testing with sample JSON file...")
        load_sample_json_and_insert()
        
        print("\n📊 Current collection statistics:")
        show_collection_stats()
        
        print("\n✅ MongoDB integration test completed!")
        print("\n💡 To run the scraper with MongoDB:")
        print("   scrapy crawl nkp_url_collector")
        print("\n💡 To check MongoDB stats:")
        print("   python mongodb_utils.py stats")
        
    else:
        print("\n❌ MongoDB test failed")
        print("\n💡 To install MongoDB:")
        print("   sudo apt update")
        print("   sudo apt install mongodb")
        print("   sudo systemctl start mongod")
        print("   sudo systemctl enable mongod")


if __name__ == "__main__":
    main()
