#!/usr/bin/env python3
"""
Debug article content extraction
"""

import requests
import re
import time
from pathlib import Path


def debug_article_extraction():
    """Debug what's happening with article extraction"""
    
    # Test with the specific article that's failing
    article_url = "https://nkp.gov.np/full_detail/8982"
    
    print(f"=== DEBUGGING ARTICLE EXTRACTION ===")
    print(f"URL: {article_url}")
    print()
    
    # Add delay to avoid rate limiting
    time.sleep(2)
    
    try:
        # Fetch the article with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        response = requests.get(article_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"✅ Successfully fetched article")
        print(f"Status: {response.status_code}")
        print(f"Content length: {len(response.text)} chars")
        print()
        
        # Check for rate limiting
        if "Request Rejected" in response.text:
            print("❌ Rate limiting detected!")
            print(f"Response: {response.text}")
            return
        
        # Check for article tag
        if '<article' in response.text.lower():
            print("✅ Found <article> tag")
            
            # Find all article tags
            article_pattern = r'<article[^>]*>(.*?)</article>'
            matches = re.findall(article_pattern, response.text, re.DOTALL | re.IGNORECASE)
            
            print(f"Found {len(matches)} article sections")
            
            if matches:
                article_content = matches[0]
                print(f"Article content length: {len(article_content)} chars")
                
                # Show first 500 chars of article content
                print(f"\nFirst 500 chars of article content:")
                print("=" * 50)
                print(article_content[:500])
                print("=" * 50)
                
                # Check for specific content patterns
                patterns_to_check = [
                    r'<div[^>]*id="[^"]*faisala[^"]*"',
                    r'<div[^>]*class="[^"]*faisala[^"]*"',
                    r'<p[^>]*>',
                    r'निर्णय',
                    r'फैसला',
                    r'अदालत'
                ]
                
                print(f"\nChecking for content patterns:")
                for pattern in patterns_to_check:
                    matches = re.findall(pattern, article_content, re.IGNORECASE)
                    print(f"  {pattern}: {len(matches)} matches")
                
                # Extract all text content
                # Remove HTML tags
                text_content = re.sub(r'<[^>]+>', '', article_content)
                # Clean up whitespace
                text_content = re.sub(r'\s+', ' ', text_content).strip()
                
                print(f"\nExtracted text length: {len(text_content)} chars")
                if text_content:
                    print(f"First 200 chars of text:")
                    print(text_content[:200])
                else:
                    print("❌ No text content extracted")
                
            else:
                print("❌ No article content found in matches")
        else:
            print("❌ No <article> tag found")
            
            # Show first 1000 chars of response
            print(f"\nFirst 1000 chars of response:")
            print("=" * 50)
            print(response.text[:1000])
            print("=" * 50)
        
        # Save the HTML for manual inspection
        debug_file = Path('debug_article.html')
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"\n💾 Saved full HTML to: {debug_file}")
        
    except requests.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_article_extraction()
