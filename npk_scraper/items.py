# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class ArticleItem(scrapy.Item):
    """Main item for Nepal Supreme Court articles"""
    # Basic info
    article_id = scrapy.Field()
    url = scrapy.Field()
    title = scrapy.Field()

    # Classification
    year = scrapy.Field()
    month = scrapy.Field()
    mudda_type = scrapy.Field()

    # Metadata
    decision_date = scrapy.Field()
    views = scrapy.Field()
    court_type = scrapy.Field()
    parties = scrapy.Field()

    # Content
    content = scrapy.Field()  # Full text content
    structured_content = scrapy.Field()  # Structured sections

    # Extraction info
    content_extracted = scrapy.Field()
    extraction_info = scrapy.Field()
    scraped_at = scrapy.Field()
