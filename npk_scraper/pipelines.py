# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import json
from datetime import datetime
from pathlib import Path
from itemadapter import ItemAdapter
from .items import ArticleItem


class NkpScraperPipeline:
    """Simple pipeline for NKP scraper items"""

    def process_item(self, item, spider):
        """Process scraped items"""
        adapter = ItemAdapter(item)
        
        if isinstance(item, ArticleItem):
            spider.logger.info(f"Processing article: {adapter.get('article_id')}")
        
        return item
